package com.zsmall.activity.biz.util;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 延迟消息监控工具
 * 用于监控和诊断延迟消息的处理情况
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
public class DelayMessageMonitor {
    
    /**
     * 消息发送统计
     */
    private static final AtomicLong SENT_COUNT = new AtomicLong(0);
    
    /**
     * 消息接收统计
     */
    private static final AtomicLong RECEIVED_COUNT = new AtomicLong(0);
    
    /**
     * 消息处理成功统计
     */
    private static final AtomicLong SUCCESS_COUNT = new AtomicLong(0);
    
    /**
     * 消息处理失败统计
     */
    private static final AtomicLong FAILED_COUNT = new AtomicLong(0);
    
    /**
     * 消息延迟统计（消息ID -> 发送时间）
     */
    private static final ConcurrentHashMap<String, Long> MESSAGE_SEND_TIME = new ConcurrentHashMap<>();
    
    /**
     * 记录消息发送
     * 
     * @param activityCode 活动编码
     * @param delayTime 延迟时间（毫秒）
     * @param endTime 活动结束时间
     */
    public static void recordMessageSent(String activityCode, long delayTime, Date endTime) {
        long sendTime = System.currentTimeMillis();
        MESSAGE_SEND_TIME.put(activityCode, sendTime);
        SENT_COUNT.incrementAndGet();
        
        log.info("延迟消息发送记录: 活动编码={}, 延迟时间={}ms ({}), 结束时间={}, 预计到达时间={}", 
                activityCode, 
                delayTime, 
                DelayTimeCalculator.formatDelayTime(delayTime),
                DateUtil.formatDateTime(endTime),
                DateUtil.formatDateTime(new Date(sendTime + delayTime)));
    }
    
    /**
     * 记录消息接收
     * 
     * @param activityCode 活动编码
     * @param message RabbitMQ消息对象
     */
    public static void recordMessageReceived(String activityCode, Message message) {
        long receiveTime = System.currentTimeMillis();
        Long sendTime = MESSAGE_SEND_TIME.get(activityCode);
        RECEIVED_COUNT.incrementAndGet();
        
        if (sendTime != null) {
            long actualDelay = receiveTime - sendTime;
            MessageProperties properties = message.getMessageProperties();
            String expectedDelayStr = properties.getExpiration();
            long expectedDelay = expectedDelayStr != null ? Long.parseLong(expectedDelayStr) : 0;
            long delayDifference = actualDelay - expectedDelay;
            
            log.info("延迟消息接收记录: 活动编码={}, 预期延迟={}ms, 实际延迟={}ms, 延迟差异={}ms, 消息时间戳={}", 
                    activityCode, 
                    expectedDelay, 
                    actualDelay, 
                    delayDifference,
                    properties.getTimestamp() != null ? DateUtil.formatDateTime(properties.getTimestamp()) : "无");
            
            // 如果延迟差异超过5秒，记录警告
            if (Math.abs(delayDifference) > 5000) {
                log.warn("延迟消息时间差异较大: 活动编码={}, 延迟差异={}ms", activityCode, delayDifference);
            }
        } else {
            log.warn("未找到消息发送记录: 活动编码={}", activityCode);
        }
    }
    
    /**
     * 记录消息处理成功
     * 
     * @param activityCode 活动编码
     * @param processingTime 处理耗时（毫秒）
     */
    public static void recordMessageSuccess(String activityCode, long processingTime) {
        SUCCESS_COUNT.incrementAndGet();
        MESSAGE_SEND_TIME.remove(activityCode); // 清理记录
        
        log.info("延迟消息处理成功: 活动编码={}, 处理耗时={}ms", activityCode, processingTime);
        
        // 如果处理时间超过10秒，记录警告
        if (processingTime > 10000) {
            log.warn("消息处理耗时较长: 活动编码={}, 处理耗时={}ms", activityCode, processingTime);
        }
    }
    
    /**
     * 记录消息处理失败
     * 
     * @param activityCode 活动编码
     * @param processingTime 处理耗时（毫秒）
     * @param error 错误信息
     */
    public static void recordMessageFailed(String activityCode, long processingTime, Throwable error) {
        FAILED_COUNT.incrementAndGet();
        MESSAGE_SEND_TIME.remove(activityCode); // 清理记录
        
        log.error("延迟消息处理失败: 活动编码={}, 处理耗时={}ms, 错误={}", 
                activityCode, processingTime, error.getMessage());
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息字符串
     */
    public static String getStatistics() {
        long sent = SENT_COUNT.get();
        long received = RECEIVED_COUNT.get();
        long success = SUCCESS_COUNT.get();
        long failed = FAILED_COUNT.get();
        long pending = MESSAGE_SEND_TIME.size();
        
        return String.format("延迟消息统计: 已发送=%d, 已接收=%d, 处理成功=%d, 处理失败=%d, 待处理=%d", 
                sent, received, success, failed, pending);
    }
    
    /**
     * 清理过期的消息记录（超过1小时的记录）
     */
    public static void cleanupExpiredRecords() {
        long currentTime = System.currentTimeMillis();
        long expireTime = 60 * 60 * 1000; // 1小时
        
        MESSAGE_SEND_TIME.entrySet().removeIf(entry -> {
            boolean expired = (currentTime - entry.getValue()) > expireTime;
            if (expired) {
                log.debug("清理过期消息记录: 活动编码={}", entry.getKey());
            }
            return expired;
        });
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStatistics() {
        SENT_COUNT.set(0);
        RECEIVED_COUNT.set(0);
        SUCCESS_COUNT.set(0);
        FAILED_COUNT.set(0);
        MESSAGE_SEND_TIME.clear();
        log.info("延迟消息统计信息已重置");
    }
}
