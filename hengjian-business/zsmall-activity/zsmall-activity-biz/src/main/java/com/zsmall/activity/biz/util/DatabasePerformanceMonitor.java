package com.zsmall.activity.biz.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据库性能监控工具
 * 用于监控数据库操作的性能
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@Component
public class DatabasePerformanceMonitor {
    
    /**
     * 查询计数器
     */
    private static final ConcurrentHashMap<String, AtomicLong> QUERY_COUNTERS = new ConcurrentHashMap<>();
    
    /**
     * 查询耗时统计
     */
    private static final ConcurrentHashMap<String, AtomicLong> QUERY_TIME_STATS = new ConcurrentHashMap<>();
    
    /**
     * 慢查询阈值（毫秒）
     */
    private static final long SLOW_QUERY_THRESHOLD = 1000L;
    
    /**
     * 记录查询开始
     * 
     * @param queryName 查询名称
     * @return 开始时间戳
     */
    public static long recordQueryStart(String queryName) {
        QUERY_COUNTERS.computeIfAbsent(queryName, k -> new AtomicLong(0)).incrementAndGet();
        return System.currentTimeMillis();
    }
    
    /**
     * 记录查询结束
     * 
     * @param queryName 查询名称
     * @param startTime 开始时间戳
     */
    public static void recordQueryEnd(String queryName, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        QUERY_TIME_STATS.computeIfAbsent(queryName + "_total_time", k -> new AtomicLong(0))
                        .addAndGet(duration);
        
        // 记录慢查询
        if (duration > SLOW_QUERY_THRESHOLD) {
            log.warn("慢查询检测: 查询={}, 耗时={}ms", queryName, duration);
            QUERY_COUNTERS.computeIfAbsent(queryName + "_slow_count", k -> new AtomicLong(0))
                          .incrementAndGet();
        }
    }
    
    /**
     * 监控方法执行
     * 
     * @param methodName 方法名称
     * @param runnable 要执行的方法
     */
    public static void monitorMethod(String methodName, Runnable runnable) {
        long startTime = recordQueryStart(methodName);
        try {
            runnable.run();
        } finally {
            recordQueryEnd(methodName, startTime);
        }
    }
    
    /**
     * 监控方法执行（带返回值）
     * 
     * @param methodName 方法名称
     * @param supplier 要执行的方法
     * @param <T> 返回值类型
     * @return 方法执行结果
     */
    public static <T> T monitorMethod(String methodName, java.util.function.Supplier<T> supplier) {
        long startTime = recordQueryStart(methodName);
        try {
            return supplier.get();
        } finally {
            recordQueryEnd(methodName, startTime);
        }
    }
    
    /**
     * 获取查询统计信息
     * 
     * @return 统计信息
     */
    public static String getQueryStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("数据库查询统计:\n");
        
        QUERY_COUNTERS.forEach((queryName, count) -> {
            if (!queryName.endsWith("_slow_count")) {
                long totalTime = QUERY_TIME_STATS.getOrDefault(queryName + "_total_time", new AtomicLong(0)).get();
                long slowCount = QUERY_COUNTERS.getOrDefault(queryName + "_slow_count", new AtomicLong(0)).get();
                long avgTime = count.get() > 0 ? totalTime / count.get() : 0;
                
                stats.append(String.format("  %s: 执行次数=%d, 总耗时=%dms, 平均耗时=%dms, 慢查询次数=%d\n",
                    queryName, count.get(), totalTime, avgTime, slowCount));
            }
        });
        
        return stats.toString();
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStatistics() {
        QUERY_COUNTERS.clear();
        QUERY_TIME_STATS.clear();
        log.info("数据库性能统计信息已重置");
    }
    
    /**
     * 检查是否有性能问题
     * 
     * @return 性能问题报告
     */
    public static String checkPerformanceIssues() {
        StringBuilder issues = new StringBuilder();
        
        QUERY_COUNTERS.forEach((queryName, count) -> {
            if (queryName.endsWith("_slow_count") && count.get() > 0) {
                String originalQueryName = queryName.replace("_slow_count", "");
                long totalCount = QUERY_COUNTERS.getOrDefault(originalQueryName, new AtomicLong(0)).get();
                double slowRatio = totalCount > 0 ? (double) count.get() / totalCount * 100 : 0;
                
                if (slowRatio > 10) { // 慢查询比例超过10%
                    issues.append(String.format("警告: %s 慢查询比例过高 %.2f%% (%d/%d)\n",
                        originalQueryName, slowRatio, count.get(), totalCount));
                }
            }
        });
        
        // 检查高频查询
        QUERY_COUNTERS.forEach((queryName, count) -> {
            if (!queryName.endsWith("_slow_count") && count.get() > 1000) {
                issues.append(String.format("注意: %s 查询频率过高 %d次\n", queryName, count.get()));
            }
        });
        
        return issues.length() > 0 ? issues.toString() : "未发现明显性能问题";
    }
}
