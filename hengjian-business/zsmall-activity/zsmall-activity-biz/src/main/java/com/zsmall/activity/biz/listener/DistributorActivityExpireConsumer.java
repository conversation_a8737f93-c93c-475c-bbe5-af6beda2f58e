package com.zsmall.activity.biz.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.activity.biz.util.DelayMessageMonitor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 分销商活动过期消息消费者
 * 监听活动过期消息，具体业务逻辑由用户自行实现
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DistributorActivityExpireConsumer {
    @Resource
    private ProductActiveService productActiveService;
    /**
     * 监听活动过期消息
     * 当消息TTL过期时，消息会被转发到这个队列
     *
     * @param message 消息对象
     * @param channel RabbitMQ通道
     * @param deliveryTag 消息投递标签
     */
    @RabbitHandler
    @RabbitListener(
        queues = RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_PROCESS_QUEUE,
        containerFactory = "distributorActivityExpireListenerContainerFactory"
    )
    public void handleActivityExpire(Message message,
                                   @Header(AmqpHeaders.CHANNEL) Channel channel,
                                   @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        String distributorActivityCode = null;
        long startTime = System.currentTimeMillis();
        try {
            // 获取消息内容（活动编码）
            String messageBody = new String(message.getBody());
            distributorActivityCode = messageBody.trim();
            log.info("接收到活动过期消息: {}, 接收时间: {}", distributorActivityCode, DateUtil.now());

            // 记录消息接收监控
            DelayMessageMonitor.recordMessageReceived(distributorActivityCode, message);

            if (StrUtil.isBlank(distributorActivityCode)) {
                log.warn("接收到空的活动过期消息，跳过处理");
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 处理活动过期逻辑
            productActiveService.handleActivityExpire(distributorActivityCode);

            // 手动确认消息
            channel.basicAck(deliveryTag, false);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("活动过期处理完成: {}, 处理耗时: {}ms", distributorActivityCode, processingTime);

            // 记录消息处理成功监控
            DelayMessageMonitor.recordMessageSuccess(distributorActivityCode, processingTime);
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("处理活动过期消息失败: {}, 处理耗时: {}ms", distributorActivityCode, processingTime, e);

            // 记录消息处理失败监控
            DelayMessageMonitor.recordMessageFailed(distributorActivityCode, processingTime, e);
            try {
                // 拒绝消息，不重新入队（避免无限重试）
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioException) {
                log.error("拒绝消息失败: {}", distributorActivityCode, ioException);
            }
        }
    }

}
