package com.zsmall.activity.biz.util;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 延迟时间计算工具类
 * 用于计算活动过期延迟消息的TTL时间
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
public class DelayTimeCalculator {
    
    /**
     * 最小延迟时间（毫秒）- 1秒
     */
    private static final long MIN_DELAY_TIME = 1000L;
    
    /**
     * 最大延迟时间（毫秒）- 30天
     */
    private static final long MAX_DELAY_TIME = 30L * 24L * 60L * 60L * 1000L;
    
    /**
     * 计算延迟时间
     * 
     * @param endTime 结束时间
     * @return 延迟时间（毫秒），如果已过期则返回0
     */
    public static long calculateDelayTime(Date endTime) {
        if (endTime == null) {
            log.warn("结束时间为空，无法计算延迟时间");
            return 0L;
        }
        
        long currentTime = System.currentTimeMillis();
        long endTimeMillis = endTime.getTime();
        long delayTime = endTimeMillis - currentTime;
        
        log.debug("计算延迟时间: 当前时间={}, 结束时间={}, 延迟时间={}ms", 
                DateUtil.formatDateTime(new Date(currentTime)), 
                DateUtil.formatDateTime(endTime), 
                delayTime);
        
        // 如果已过期，返回0
        if (delayTime <= 0) {
            log.info("活动已过期: 结束时间={}, 当前时间={}", 
                    DateUtil.formatDateTime(endTime), 
                    DateUtil.formatDateTime(new Date(currentTime)));
            return 0L;
        }
        
        return delayTime;
    }
    
    /**
     * 验证延迟时间是否合理
     * 
     * @param delayTime 延迟时间（毫秒）
     * @return true-合理，false-不合理
     */
    public static boolean isValidDelayTime(long delayTime) {
        if (delayTime < MIN_DELAY_TIME) {
            log.warn("延迟时间过短: {}ms，最小延迟时间: {}ms", delayTime, MIN_DELAY_TIME);
            return false;
        }
        
        if (delayTime > MAX_DELAY_TIME) {
            log.warn("延迟时间过长: {}ms，最大延迟时间: {}ms", delayTime, MAX_DELAY_TIME);
            return false;
        }
        
        return true;
    }
    
    /**
     * 计算并验证延迟时间
     * 
     * @param endTime 结束时间
     * @return 有效的延迟时间（毫秒），如果无效则返回0
     */
    public static long calculateAndValidateDelayTime(Date endTime) {
        long delayTime = calculateDelayTime(endTime);
        
        if (delayTime <= 0) {
            return 0L;
        }
        
        if (!isValidDelayTime(delayTime)) {
            log.error("延迟时间验证失败: {}ms，结束时间: {}", 
                    delayTime, DateUtil.formatDateTime(endTime));
            return 0L;
        }
        
        return delayTime;
    }
    
    /**
     * 格式化延迟时间为可读字符串
     * 
     * @param delayTimeMillis 延迟时间（毫秒）
     * @return 格式化后的字符串
     */
    public static String formatDelayTime(long delayTimeMillis) {
        if (delayTimeMillis <= 0) {
            return "0ms";
        }
        
        long seconds = delayTimeMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天%d小时%d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
