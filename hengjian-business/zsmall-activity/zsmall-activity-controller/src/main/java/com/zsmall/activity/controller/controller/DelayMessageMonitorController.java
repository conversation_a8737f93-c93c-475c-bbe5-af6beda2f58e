package com.zsmall.activity.controller.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.activity.biz.util.DelayMessageMonitor;
import com.zsmall.activity.biz.util.DelayTimeCalculator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 延迟消息监控控制器
 * 用于监控和测试延迟消息的处理情况
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@RestController
@RequestMapping("/delayMessageMonitor")
@RequiredArgsConstructor
@Tag(name = "延迟消息监控", description = "延迟消息监控和测试接口")
public class DelayMessageMonitorController {
    
    private final ProductActiveService productActiveService;
    
    /**
     * 获取延迟消息统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取延迟消息统计信息")
    public R<String> getStatistics() {
        try {
            String statistics = DelayMessageMonitor.getStatistics();
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取延迟消息统计信息失败", e);
            return R.fail("获取统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置延迟消息统计信息
     */
    @PostMapping("/resetStatistics")
    @Operation(summary = "重置延迟消息统计信息")
    public R<String> resetStatistics() {
        try {
            DelayMessageMonitor.resetStatistics();
            return R.ok("统计信息已重置");
        } catch (Exception e) {
            log.error("重置延迟消息统计信息失败", e);
            return R.fail("重置统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理过期的消息记录
     */
    @PostMapping("/cleanupExpiredRecords")
    @Operation(summary = "清理过期的消息记录")
    public R<String> cleanupExpiredRecords() {
        try {
            DelayMessageMonitor.cleanupExpiredRecords();
            return R.ok("过期记录已清理");
        } catch (Exception e) {
            log.error("清理过期记录失败", e);
            return R.fail("清理过期记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试延迟时间计算
     */
    @PostMapping("/testDelayTimeCalculation")
    @Operation(summary = "测试延迟时间计算")
    public R<Map<String, Object>> testDelayTimeCalculation(@RequestParam String endTimeStr) {
        try {
            Date endTime = DateUtil.parse(endTimeStr, "yyyy-MM-dd HH:mm:ss");
            long delayTime = DelayTimeCalculator.calculateDelayTime(endTime);
            boolean isValid = DelayTimeCalculator.isValidDelayTime(delayTime);
            String formattedDelay = DelayTimeCalculator.formatDelayTime(delayTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("endTime", DateUtil.formatDateTime(endTime));
            result.put("currentTime", DateUtil.formatDateTime(new Date()));
            result.put("delayTimeMs", delayTime);
            result.put("formattedDelayTime", formattedDelay);
            result.put("isValid", isValid);
            
            return R.ok(result);
        } catch (Exception e) {
            log.error("测试延迟时间计算失败", e);
            return R.fail("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送测试延迟消息
     */
    @PostMapping("/sendTestDelayMessage")
    @Operation(summary = "发送测试延迟消息")
    public R<String> sendTestDelayMessage(
            @RequestParam String distributorActivityCode,
            @RequestParam String endTime) {
        try {
            String tenantId = LoginHelper.getTenantId();
            if (StrUtil.isEmpty(tenantId)) {
                return R.fail("请登录后使用");
            }
            
            productActiveService.sendDistributorActivityExpire(distributorActivityCode, endTime);
            return R.ok("测试延迟消息发送成功");
        } catch (Exception e) {
            log.error("发送测试延迟消息失败", e);
            return R.fail("发送测试消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取延迟消息处理建议
     */
    @GetMapping("/getOptimizationSuggestions")
    @Operation(summary = "获取延迟消息处理优化建议")
    public R<Map<String, Object>> getOptimizationSuggestions() {
        try {
            Map<String, Object> suggestions = new HashMap<>();
            
            // 基本建议
            suggestions.put("rabbitMQ配置建议", Map.of(
                "消费者并发数", "建议设置为2-5个并发消费者",
                "预取数量", "建议设置为10-50",
                "确认模式", "使用手动确认模式确保消息可靠性"
            ));
            
            suggestions.put("延迟时间优化", Map.of(
                "最小延迟时间", "1秒",
                "最大延迟时间", "30天",
                "时间计算精度", "毫秒级别"
            ));
            
            suggestions.put("监控建议", Map.of(
                "消息处理时间", "正常应在10秒内完成",
                "延迟差异", "实际延迟与预期延迟差异应在5秒内",
                "统计信息", "定期查看消息处理统计"
            ));
            
            suggestions.put("故障排查", Map.of(
                "检查DelayTimeCalculator类", "确保类存在且方法正常",
                "检查RabbitMQ连接", "确保连接正常且队列配置正确",
                "检查消息处理逻辑", "确保没有阻塞操作",
                "检查数据库事务", "避免长时间锁等待"
            ));
            
            return R.ok(suggestions);
        } catch (Exception e) {
            log.error("获取优化建议失败", e);
            return R.fail("获取建议失败: " + e.getMessage());
        }
    }
}
