package com.zsmall.activity.controller.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.activity.biz.util.DatabasePerformanceMonitor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 活动性能诊断控制器
 * 用于诊断活动过期处理的性能问题
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@RestController
@RequestMapping("/activityPerformanceDiagnostic")
@RequiredArgsConstructor
@Tag(name = "活动性能诊断", description = "活动过期处理性能诊断接口")
public class ActivityPerformanceDiagnosticController {

    @Resource
    private ProductActiveService productActiveService;

    /**
     * 诊断活动过期处理性能
     */
    @PostMapping("/diagnoseActivityExpirePerformance")
    @Operation(summary = "诊断活动过期处理性能")
    public R<Map<String, Object>> diagnoseActivityExpirePerformance(@RequestParam String distributorActivityCode) {
        try {
            String tenantId = LoginHelper.getTenantId();
            if (StrUtil.isEmpty(tenantId)) {
                return R.fail("请登录后使用");
            }

            Map<String, Object> diagnosticResult = new HashMap<>();
            long startTime = System.currentTimeMillis();

            // 1. 测试数据库查询性能
            long dbQueryStart = System.currentTimeMillis();
            try {
                // 模拟查询分销商活动
                var activity = productActiveService.getDistributorProductActivityService()
                    .getByActivityCode(distributorActivityCode);
                long dbQueryTime = System.currentTimeMillis() - dbQueryStart;
                diagnosticResult.put("数据库查询耗时(ms)", dbQueryTime);

                if (activity == null) {
                    diagnosticResult.put("诊断结果", "活动不存在");
                    return R.ok(diagnosticResult);
                }

                // 2. 测试库存查询性能
                long stockQueryStart = System.currentTimeMillis();
                var stocks = productActiveService.getDistributorProductActivityStockService()
                    .queryListByDistributorProductActivityIds(List.of(activity.getId()));
                long stockQueryTime = System.currentTimeMillis() - stockQueryStart;
                diagnosticResult.put("库存查询耗时(ms)", stockQueryTime);
                diagnosticResult.put("库存记录数量", stocks != null ? stocks.size() : 0);

                // 3. 分析潜在性能问题
                analyzePotentialIssues(diagnosticResult, dbQueryTime, stockQueryTime,
                    stocks != null ? stocks.size() : 0);

            } catch (Exception e) {
                long dbQueryTime = System.currentTimeMillis() - dbQueryStart;
                diagnosticResult.put("数据库查询耗时(ms)", dbQueryTime);
                diagnosticResult.put("数据库查询异常", e.getMessage());
            }

            long totalTime = System.currentTimeMillis() - startTime;
            diagnosticResult.put("总诊断耗时(ms)", totalTime);
            diagnosticResult.put("诊断时间", DateUtil.now());

            return R.ok(diagnosticResult);

        } catch (Exception e) {
            log.error("诊断活动过期处理性能失败", e);
            return R.fail("诊断失败: " + e.getMessage());
        }
    }

    /**
     * 模拟活动过期处理，测试实际性能
     */
    @PostMapping("/simulateActivityExpireProcessing")
    @Operation(summary = "模拟活动过期处理性能测试")
    public R<Map<String, Object>> simulateActivityExpireProcessing(@RequestParam String distributorActivityCode) {
        try {
            String tenantId = LoginHelper.getTenantId();
            if (StrUtil.isEmpty(tenantId)) {
                return R.fail("请登录后使用");
            }

            Map<String, Object> result = new HashMap<>();
            long startTime = System.currentTimeMillis();

            // 使用异步方式测试，避免阻塞
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 注意：这里只是模拟，不会真正执行过期处理
                    log.info("模拟活动过期处理开始: {}", distributorActivityCode);

                    // 模拟各个步骤的耗时
                    Thread.sleep(100); // 模拟数据库查询
                    Thread.sleep(200); // 模拟库存处理
                    Thread.sleep(50);  // 模拟状态更新

                    log.info("模拟活动过期处理完成: {}", distributorActivityCode);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("模拟处理被中断", e);
                }
            });

            try {
                // 等待最多10秒
                future.get(10, TimeUnit.SECONDS);
                long processingTime = System.currentTimeMillis() - startTime;

                result.put("模拟处理耗时(ms)", processingTime);
                result.put("处理状态", "成功");
                result.put("建议", processingTime > 5000 ? "处理时间过长，需要优化" : "处理时间正常");

            } catch (Exception e) {
                long processingTime = System.currentTimeMillis() - startTime;
                result.put("模拟处理耗时(ms)", processingTime);
                result.put("处理状态", "超时或异常");
                result.put("异常信息", e.getMessage());
            }

            return R.ok(result);

        } catch (Exception e) {
            log.error("模拟活动过期处理失败", e);
            return R.fail("模拟处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统资源状态
     */
    @GetMapping("/getSystemResourceStatus")
    @Operation(summary = "获取系统资源状态")
    public R<Map<String, Object>> getSystemResourceStatus() {
        try {
            Map<String, Object> resourceStatus = new HashMap<>();

            // JVM内存信息
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();

            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("总内存(MB)", totalMemory / 1024 / 1024);
            memoryInfo.put("已用内存(MB)", usedMemory / 1024 / 1024);
            memoryInfo.put("空闲内存(MB)", freeMemory / 1024 / 1024);
            memoryInfo.put("最大内存(MB)", maxMemory / 1024 / 1024);
            memoryInfo.put("内存使用率(%)", String.format("%.2f", (double) usedMemory / totalMemory * 100));

            resourceStatus.put("内存状态", memoryInfo);

            // 线程信息
            Map<String, Object> threadInfo = new HashMap<>();
            threadInfo.put("活跃线程数", Thread.activeCount());
            threadInfo.put("可用处理器数", runtime.availableProcessors());

            resourceStatus.put("线程状态", threadInfo);

            // 系统建议
            double memoryUsageRate = (double) usedMemory / totalMemory;
            if (memoryUsageRate > 0.8) {
                resourceStatus.put("内存建议", "内存使用率过高，可能影响性能");
            } else {
                resourceStatus.put("内存建议", "内存使用正常");
            }

            return R.ok(resourceStatus);

        } catch (Exception e) {
            log.error("获取系统资源状态失败", e);
            return R.fail("获取资源状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据库性能统计
     */
    @GetMapping("/getDatabasePerformanceStats")
    @Operation(summary = "获取数据库性能统计")
    public R<Map<String, Object>> getDatabasePerformanceStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("查询统计", DatabasePerformanceMonitor.getQueryStatistics());
            stats.put("性能问题检查", DatabasePerformanceMonitor.checkPerformanceIssues());
            stats.put("统计时间", DateUtil.now());

            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取数据库性能统计失败", e);
            return R.fail("获取统计失败: " + e.getMessage());
        }
    }

    /**
     * 重置数据库性能统计
     */
    @PostMapping("/resetDatabasePerformanceStats")
    @Operation(summary = "重置数据库性能统计")
    public R<String> resetDatabasePerformanceStats() {
        try {
            DatabasePerformanceMonitor.resetStatistics();
            return R.ok("数据库性能统计已重置");
        } catch (Exception e) {
            log.error("重置数据库性能统计失败", e);
            return R.fail("重置失败: " + e.getMessage());
        }
    }

    /**
     * 分析潜在性能问题
     */
    private void analyzePotentialIssues(Map<String, Object> result, long dbQueryTime,
                                      long stockQueryTime, int stockCount) {
        StringBuilder issues = new StringBuilder();
        StringBuilder suggestions = new StringBuilder();

        // 分析数据库查询性能
        if (dbQueryTime > 1000) {
            issues.append("数据库查询耗时过长(>1s); ");
            suggestions.append("检查数据库索引和查询语句; ");
        }

        // 分析库存查询性能
        if (stockQueryTime > 500) {
            issues.append("库存查询耗时过长(>500ms); ");
            suggestions.append("优化库存查询逻辑; ");
        }

        // 分析库存记录数量
        if (stockCount > 100) {
            issues.append("库存记录数量过多(>100); ");
            suggestions.append("考虑分批处理库存记录; ");
        }

        result.put("发现的问题", issues.length() > 0 ? issues.toString() : "无明显性能问题");
        result.put("优化建议", suggestions.length() > 0 ? suggestions.toString() : "当前性能表现良好");
    }
}
